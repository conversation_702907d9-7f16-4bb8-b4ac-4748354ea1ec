"""
Phase 1 Integration for ML Core

This module provides Phase 1 enhanced versions of the core ML functions
that integrate advanced data preprocessing to eliminate non-finite gradient issues.

Usage:
    Replace calls to impute_logs_deep with impute_logs_deep_phase1
"""

import numpy as np
import pandas as pd
import torch
from typing import Dict, Any, Optional, List, Tuple

# Import original functions
from ml_core import impute_logs_deep as original_impute_logs_deep
from data_handler import create_sequences, normalize_data, introduce_missingness

# Import Phase 1 preprocessing
try:
    from utils.stability_core import (
        phase1_preprocessing_pipeline,
        enhanced_validate_sequences,
        get_recommended_preprocessing_config,
        numerical_stability_check
    )
    PHASE1_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Phase 1 preprocessing not available: {e}")
    PHASE1_AVAILABLE = False


def impute_logs_deep_phase1(df: pd.DataFrame, 
                           feature_cols: List[str], 
                           target_col: str, 
                           model_config: Dict[str, Any], 
                           hparams: Dict[str, Any], 
                           use_enhanced_preprocessing: bool = True) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Enhanced version of impute_logs_deep with Phase 1 preprocessing integration.
    
    This function addresses the non-finite gradient issues by:
    1. Applying Phase 1 preprocessing to eliminate problematic data
    2. Validating sequences before training
    3. Monitoring for stability issues during processing
    
    Args:
        df: Input dataframe
        feature_cols: Feature column names
        target_col: Target column name
        model_config: Model configuration
        hparams: Hyperparameters
        use_enhanced_preprocessing: Whether to use enhanced preprocessing
        
    Returns:
        Tuple of (result_dataframe, model_results)
    """
    if not PHASE1_AVAILABLE:
        print("⚠️ Phase 1 preprocessing not available, falling back to original function")
        return original_impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing)
    
    print("🚀 Starting Phase 1 Enhanced Deep Learning Training...")
    print(f"   Model: {model_config['name']}")
    print(f"   Target: {target_col}")
    print(f"   Features: {feature_cols}")
    
    # Step 1: Original preprocessing (up to sequence creation)
    print("\n📊 Step 1: Initial Data Preparation...")
    
    # Get all features including target
    all_features = feature_cols + [target_col]
    
    # Normalize data using existing function
    df_scaled, scalers = normalize_data(df, all_features, use_enhanced=use_enhanced_preprocessing)
    
    # Create sequences using existing function
    train_sequences_true, metadata = create_sequences(
        df_scaled, 'WELL', all_features, 
        sequence_len=hparams.get('sequence_len', 64),
        use_enhanced=use_enhanced_preprocessing
    )
    
    if train_sequences_true.shape[0] == 0:
        print("❌ ERROR: Cannot create training sequences. Falling back to original function.")
        return original_impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing)
    
    print(f"   Created {train_sequences_true.shape[0]} sequences")
    print(f"   Sequence shape: {train_sequences_true.shape}")
    
    # Step 2: Apply Phase 1 Preprocessing Pipeline
    print("\n🔍 Step 2: Phase 1 Advanced Preprocessing...")
    
    # Get recommended configuration for this dataset
    missing_rate = np.sum(np.isnan(train_sequences_true)) / np.prod(train_sequences_true.shape)
    config = get_recommended_preprocessing_config(
        dataset_size=train_sequences_true.shape[0],
        missing_rate=missing_rate,
        feature_types=all_features
    )
    
    print(f"   Dataset characteristics:")
    print(f"     • Sequences: {train_sequences_true.shape[0]}")
    print(f"     • Missing rate: {missing_rate:.1%}")
    print(f"     • Recommended config: {config}")
    
    # Apply Phase 1 preprocessing pipeline
    processed_sequences, processing_metadata = phase1_preprocessing_pipeline(
        sequences=train_sequences_true,
        feature_names=all_features,
        normalization_method=config.get('normalization_method', 'robust_standard'),
        missing_encoding_method=config.get('missing_encoding_method', 'learnable_embedding'),
        validate_ranges=config.get('validate_ranges', True),
        generate_report=False  # Skip detailed report for speed
    )
    
    print(f"✅ Phase 1 preprocessing completed:")
    print(f"   • Data quality score: {processing_metadata['reports']['validation']['data_quality_score']:.3f}")
    print(f"   • Missing rate: {processing_metadata['reports']['encoding']['missing_rate_before']:.1%} → {processing_metadata['reports']['encoding']['missing_rate_after']:.1%}")
    print(f"   • Final stability: {'✅ STABLE' if processing_metadata['final_stability']['is_stable'] else '❌ UNSTABLE'}")
    
    # Step 3: Create missing sequences for training
    print("\n❓ Step 3: Creating Training Sequences with Missing Values...")
    
    # Use the processed sequences as the "true" sequences
    train_sequences_missing = introduce_missingness(
        processed_sequences, 
        target_col_name=target_col,
        feature_names=all_features, 
        missing_rate=0.3, 
        use_enhanced=use_enhanced_preprocessing
    )
    
    print(f"   Training sequences with missing values: {train_sequences_missing.shape}")
    
    # Step 4: Validate sequences before training
    print("\n🔧 Step 4: Pre-training Validation...")
    
    # Validate training sequences
    train_stable = enhanced_validate_sequences(train_sequences_missing, all_features)
    truth_stable = enhanced_validate_sequences(processed_sequences, all_features)
    
    print(f"   Training sequences: {'✅ STABLE' if train_stable else '❌ UNSTABLE'}")
    print(f"   Truth sequences: {'✅ STABLE' if truth_stable else '❌ UNSTABLE'}")
    
    if not (train_stable and truth_stable):
        print("⚠️ WARNING: Sequences failed stability check!")
        print("   This may indicate data quality issues that need attention.")
        
        # Perform additional diagnostics
        train_check = numerical_stability_check(train_sequences_missing, "training_sequences")
        truth_check = numerical_stability_check(processed_sequences, "truth_sequences")
        
        print(f"   Training issues: {train_check.get('issues', [])}")
        print(f"   Truth issues: {truth_check.get('issues', [])}")
    
    # Step 5: Convert to tensors and call original training function
    print("\n🚀 Step 5: Enhanced Model Training...")
    
    # Convert to tensors
    train_tensor = torch.tensor(train_sequences_missing, dtype=torch.float32)
    truth_tensor = torch.tensor(processed_sequences, dtype=torch.float32)
    
    print(f"   Training tensor shape: {train_tensor.shape}")
    print(f"   Truth tensor shape: {truth_tensor.shape}")
    print(f"   Missing values in training: {torch.isnan(train_tensor).sum().item()}")
    print(f"   Missing values in truth: {torch.isnan(truth_tensor).sum().item()}")
    
    # Create a modified model config that uses the processed data
    enhanced_model_config = model_config.copy()
    enhanced_hparams = hparams.copy()
    
    # Add Phase 1 metadata to model results
    phase1_metadata = {
        'phase1_applied': True,
        'preprocessing_config': config,
        'data_quality_score': processing_metadata['reports']['validation']['data_quality_score'],
        'missing_rate_reduction': f"{processing_metadata['reports']['encoding']['missing_rate_before']:.1%} → {processing_metadata['reports']['encoding']['missing_rate_after']:.1%}",
        'stability_check': processing_metadata['final_stability']['is_stable']
    }
    
    # Temporarily replace the sequences in a way that the original function can use them
    # We'll need to modify the approach since the original function expects DataFrame input
    
    # For now, let's create a synthetic DataFrame that represents our processed sequences
    # This is a workaround to integrate with the existing function structure
    
    print("   Preparing enhanced data for model training...")
    
    # Create a temporary DataFrame from processed sequences for compatibility
    n_sequences, seq_len, n_features = processed_sequences.shape
    
    # Flatten sequences back to DataFrame format
    flattened_data = []
    for seq_idx in range(n_sequences):
        well_name = f"WELL_{seq_idx // 10}"  # Group sequences by synthetic wells
        for time_idx in range(seq_len):
            row_data = {'WELL': well_name, 'MD': time_idx}
            for feat_idx, feat_name in enumerate(all_features):
                row_data[feat_name] = processed_sequences[seq_idx, time_idx, feat_idx]
            flattened_data.append(row_data)
    
    processed_df = pd.DataFrame(flattened_data)
    
    print(f"   Created processed DataFrame: {processed_df.shape}")
    print(f"   Wells: {processed_df['WELL'].nunique()}")
    
    # Call original function with processed data
    print("\n🎯 Calling enhanced model training...")
    try:
        result_df, model_results = original_impute_logs_deep(
            processed_df, feature_cols, target_col, enhanced_model_config, enhanced_hparams, use_enhanced_preprocessing
        )
        
        # Add Phase 1 metadata to results
        if model_results:
            model_results['phase1_metadata'] = phase1_metadata
            model_results['original_missing_count'] = int(np.sum(np.isnan(train_sequences_true)))
            model_results['processed_missing_count'] = int(np.sum(np.isnan(processed_sequences)))
            
            print("\n✅ Phase 1 Enhanced Training Completed Successfully!")
            print(f"   Original missing values: {model_results['original_missing_count']:,}")
            print(f"   Processed missing values: {model_results['processed_missing_count']:,}")
            print(f"   Data quality improvement: {phase1_metadata['data_quality_score']:.3f}")
            
        return result_df, model_results
        
    except Exception as e:
        print(f"❌ Enhanced training failed: {e}")
        print("   Falling back to original function...")
        return original_impute_logs_deep(df, feature_cols, target_col, model_config, hparams, use_enhanced_preprocessing)


def validate_batch_before_training(batch_data: torch.Tensor, 
                                 batch_idx: int, 
                                 feature_names: List[str]) -> bool:
    """
    Validate a batch before training to prevent non-finite gradient issues.
    
    Args:
        batch_data: Batch tensor to validate
        batch_idx: Batch index for logging
        feature_names: List of feature names
        
    Returns:
        True if batch is safe for training, False otherwise
    """
    if not PHASE1_AVAILABLE:
        return True  # Skip validation if Phase 1 not available
    
    is_stable = enhanced_validate_sequences(batch_data, feature_names, batch_idx)
    
    if not is_stable:
        print(f"⚠️ Batch {batch_idx} failed stability check - skipping to prevent gradient issues")
    
    return is_stable


# Export enhanced functions
__all__ = [
    'impute_logs_deep_phase1',
    'validate_batch_before_training'
]
